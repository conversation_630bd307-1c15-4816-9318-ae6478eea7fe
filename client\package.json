{"name": "browzy-client", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "npx tsc && npx vite build", "preview": "vite preview"}, "dependencies": {"@hookform/resolvers": "^5.2.0", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-aspect-ratio": "^1.1.7", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@supabase/supabase-js": "^2.52.1", "@tanstack/react-query": "^5.80.6", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "framer-motion": "^11.0.8", "lucide-react": "^0.350.0", "react": "^18.2.0", "react-day-picker": "^9.8.1", "react-dom": "^18.2.0", "react-hook-form": "^7.61.1", "tailwind-merge": "^3.3.0", "wouter": "^3.7.1", "zod": "^4.0.10"}, "devDependencies": {"@types/node": "^22.15.30", "@types/react": "^18.2.61", "@types/react-dom": "^18.2.19", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.17", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "tailwindcss-animate": "^1.0.7", "typescript": "^5.3.3", "vite": "^5.1.4"}}